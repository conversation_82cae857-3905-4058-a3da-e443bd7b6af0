// Copyright (c) 2024 Open Navigation LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "opennav_docking/dock_database.hpp"
#include <random>

namespace {
  std::string generate_random_string(size_t length = 10)
  {
      static const char charset[] =
          "**********"
          "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
          "abcdefghijklmnopqrstuvwxyz";
      static std::mt19937 rng(std::random_device{}());
      static std::uniform_int_distribution<> dist(0, sizeof(charset) - 2);
  
      std::string result;
      result.reserve(length);
      for (size_t i = 0; i < length; ++i)
          result += charset[dist(rng)];
      return result;
  }
}

namespace opennav_docking
{

DockDatabase::DockDatabase(std::shared_ptr<std::recursive_mutex> mutex)
: mutex_(mutex),
  dock_loader_("opennav_docking_core", "opennav_docking_core::ChargingDock"),
  robot_docking_half_length_(0.0),
  dock_offset_(0.0),
  dock_backwards_(true)
{}

DockDatabase::~DockDatabase()
{
  dock_instances_.clear();
  dock_plugins_.clear();
}

bool DockDatabase::initialize(
  const rclcpp_lifecycle::LifecycleNode::WeakPtr & parent,
  std::shared_ptr<tf2_ros::Buffer> tf)
{
  node_ = parent;
  tf2_buffer_ = tf;
  auto node = node_.lock();

  if (!getDockPlugins(node, tf)) {
    RCLCPP_ERROR(
      node->get_logger(),
      "An error occurred while getting the dock plugins!");
    return false;
  }

  if (!getDockInstances(node)) {
    RCLCPP_ERROR(
      node->get_logger(),
      "An error occurred while getting the dock instances!");
    return false;
  }

  nav2_util::declare_parameter_if_not_declared(
    node, "dock_database.robot_docking_half_length", rclcpp::ParameterValue(0.38));
  nav2_util::declare_parameter_if_not_declared(
    node, "dock_database.dock_offset", rclcpp::ParameterValue(0.09));
  nav2_util::declare_parameter_if_not_declared(
    node, "dock_database.publish_debug", rclcpp::ParameterValue(false));

  node->get_parameter("dock_database.robot_docking_half_length", robot_docking_half_length_);
  node->get_parameter("dock_database.dock_offset", dock_offset_);
  node->get_parameter("dock_database.publish_debug", publish_debug_);
  node->get_parameter("dock_backwards", dock_backwards_);

  RCLCPP_INFO(
    node->get_logger(),
    "Docking Server has %u dock types and %u dock instances available.",
    this->plugin_size(), this->instance_size());

  reload_db_service_ = node->create_service<opennav_docking_msgs::srv::ReloadDatabase>(
    "~/reload_database",
    std::bind(
      &DockDatabase::reloadDbCb, this,
      std::placeholders::_1, std::placeholders::_2));

  register_dock_service_ = node->create_service<opennav_docking_msgs::srv::RegisterDock>(
    "~/register_dock",
    std::bind(
      &DockDatabase::registerDockCb, this,
      std::placeholders::_1, std::placeholders::_2));
      
  get_all_docks_service_ = node->create_service<opennav_docking_msgs::srv::GetAllDocks>(
    "~/get_all_docks",
    std::bind(
      &DockDatabase::getAllDocksCb, this,
      std::placeholders::_1, std::placeholders::_2));

  update_dock_service_ = node->create_service<opennav_docking_msgs::srv::UpdateDock>(
    "~/update_dock",
    std::bind(
      &DockDatabase::updateDockCb, this,
      std::placeholders::_1, std::placeholders::_2));

  delete_dock_service_ = node->create_service<opennav_docking_msgs::srv::DeleteDock>(
    "~/delete_dock",
    std::bind(
      &DockDatabase::deleteDockCb, this,
      std::placeholders::_1, std::placeholders::_2));

  set_docks_service_ = node->create_service<opennav_docking_msgs::srv::SetDocks>(
    "~/set_docks",
    std::bind(
      &DockDatabase::setDocksCb, this,
      std::placeholders::_1, std::placeholders::_2));

  // Create debug publisher
  if (publish_debug_) {
    dock_debug_pub_ = node->create_publisher<geometry_msgs::msg::PoseArray>("~/docks_debug", 1);
  }

  RCLCPP_INFO(node->get_logger(), "Dock database initialized successfully.");
  return true;
}

void DockDatabase::activate()
{
  DockPluginMap::iterator it;
  for (it = dock_plugins_.begin(); it != dock_plugins_.end(); ++it) {
    it->second->activate();
  }
}

void DockDatabase::deactivate()
{
  DockPluginMap::iterator it;
  for (it = dock_plugins_.begin(); it != dock_plugins_.end(); ++it) {
    it->second->deactivate();
  }
}

void DockDatabase::reloadDbCb(
  const std::shared_ptr<opennav_docking_msgs::srv::ReloadDatabase::Request> request,
  std::shared_ptr<opennav_docking_msgs::srv::ReloadDatabase::Response> response)
{
  if (!mutex_->try_lock()) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Cannot reload database while docking!");
    response->success = false;
    return;
  }

  DockMap dock_instances;
  if (utils::parseDockFile(request->filepath, node_.lock(), dock_instances)) {
    dock_instances_ = dock_instances;
    response->success = true;
    mutex_->unlock();
    return;
  }
  response->success = false;
  mutex_->unlock();
}

void DockDatabase::registerDockCb(
    const std::shared_ptr<opennav_docking_msgs::srv::RegisterDock::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::RegisterDock::Response> response)
{
  if (!mutex_->try_lock()) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Cannot register dock while docking!");
    response->success = false;
    response->message = "Cannot register dock while docking!";
    return;
  }

  std::string dock_id;
  if (!registerDock(dock_id, request->display_name)) {
    response->success = false;
    response->message = "Failed to register dock!";
    mutex_->unlock();
    return;
  }
  response->success = true;
  response->dock_id = dock_id;
  response->message = "Dock registered successfully.";
  mutex_->unlock();
}

void DockDatabase::getAllDocksCb(
    const std::shared_ptr<opennav_docking_msgs::srv::GetAllDocks::Request> /*request*/,
    std::shared_ptr<opennav_docking_msgs::srv::GetAllDocks::Response> response)
{
  if (!mutex_->try_lock()) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Cannot get all docks while docking!");
    return;
  }

  response->docks.clear();
  for (const auto& pair : dock_instances_) {
    opennav_docking_msgs::msg::Dock dock_msg;
    dock_msg.dock_id = pair.first;
    dock_msg.display_name = pair.second.display_name;
    dock_msg.pose = pair.second.pose;
    response->docks.push_back(dock_msg);
  }
  mutex_->unlock();
}

void DockDatabase::updateDockCb(
    const std::shared_ptr<opennav_docking_msgs::srv::UpdateDock::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::UpdateDock::Response> response)
{
  if (!mutex_->try_lock()) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Cannot update dock while docking!");
    response->success = false;
    response->message = "Cannot update dock while docking!";
    return;
  }

  const auto & dock = request->dock;

  if (dock.dock_id.empty()) {
    response->success = false;
    response->message = "Dock ID is empty.";
    mutex_->unlock();
    return;
  }

  auto iter = dock_instances_.find(dock.dock_id);
  if (iter == dock_instances_.end()) {
    response->success = false;
    response->message = "Dock ID not found.";
    mutex_->unlock();
    return;
  }

  iter->second.display_name = dock.display_name;
  iter->second.pose = dock.pose;

  response->success = true;
  response->message = "Dock updated successfully.";
  if (publish_debug_) {
    publishDocksDebug();
  }
  mutex_->unlock();
}

void DockDatabase::deleteDockCb(
    const std::shared_ptr<opennav_docking_msgs::srv::DeleteDock::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::DeleteDock::Response> response)
{
  if (!mutex_->try_lock()) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Cannot delete dock while docking!");
    response->success = false;
    response->message = "Cannot delete dock while docking!";
    return;
  }

  if (request->delete_all) {
    dock_instances_.clear();
    response->success = true;
    response->message = "All docks deleted successfully.";
    if (publish_debug_) {
      publishDocksDebug();
    }
    mutex_->unlock();
    return;
  }

  const std::string & dock_id = request->dock_id;
  if (dock_id.empty()) {
    response->success = false;
    response->message = "Dock ID is empty.";
    mutex_->unlock();
    return;
  }
  auto it = dock_instances_.find(dock_id);
  if (it != dock_instances_.end()) {
    dock_instances_.erase(it);
    response->success = true;
    response->message = "Dock deleted successfully.";
    if (publish_debug_) {
      publishDocksDebug();
    }
  } else {
    response->success = false;
    response->message = "Dock ID not found.";
  }
  mutex_->unlock();
}

void DockDatabase::setDocksCb(
    const std::shared_ptr<opennav_docking_msgs::srv::SetDocks::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::SetDocks::Response> response)
{
  if (!mutex_->try_lock()) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Cannot set docks while docking!");
    response->success = false;
    response->message = "Cannot set docks while docking!";
    return;
  }

  const auto & docks = request->docks;
  for (const auto & dock : docks) {
    std::string dock_id = dock.dock_id;
    if (dock_id.empty()) {
      dock_id = generate_random_string();
    }
    std::string display_name = dock.display_name;
    if (display_name.empty()) {
      display_name = dock_id.size() > 6 ? dock_id.substr(dock_id.size() - 6) : dock_id;
    }

    Dock new_dock;
    new_dock.type = "simple_charging_dock";
    new_dock.frame = "map";
    new_dock.display_name = display_name;
    new_dock.pose = dock.pose;
    new_dock.plugin = dock_plugins_["simple_charging_dock"];
    dock_instances_[dock_id] = new_dock;
  }
  response->success = true;
  response->message = "Docks set successfully.";
  if (publish_debug_) {
    publishDocksDebug();
  }
  mutex_->unlock();
}

Dock * DockDatabase::findDock(const std::string & dock_id)
{
  Dock * dock_instance = findDockInstance(dock_id);
  ChargingDock::Ptr dock_plugin{nullptr};

  if (dock_instance) {
    dock_plugin = findDockPlugin(dock_instance->type);
    if (dock_plugin) {
      // Populate the plugin shared pointer
      dock_instance->plugin = dock_plugin;
      return dock_instance;
    }
    throw opennav_docking_core::DockNotValid("Dock requested has no valid plugin!");
  }
  throw opennav_docking_core::DockNotInDB("Dock ID requested is not in database!");
}

Dock * DockDatabase::findDockInstance(const std::string & dock_id)
{
  auto it = dock_instances_.find(dock_id);
  if (it != dock_instances_.end()) {
    return &(it->second);
  }
  return nullptr;
}

ChargingDock::Ptr DockDatabase::findDockPlugin(const std::string & type)
{
  // If only one dock plugin and type not set, use the default dock
  if (type.empty() && dock_plugins_.size() == 1) {
    return dock_plugins_.begin()->second;
  }

  auto it = dock_plugins_.find(type);
  if (it != dock_plugins_.end()) {
    return it->second;
  }
  return nullptr;
}

bool DockDatabase::getDockPlugins(
  const rclcpp_lifecycle::LifecycleNode::SharedPtr & node,
  std::shared_ptr<tf2_ros::Buffer> tf)
{
  std::vector<std::string> docks_plugins;
  if (!node->has_parameter("dock_plugins")) {
    node->declare_parameter("dock_plugins", rclcpp::ParameterType::PARAMETER_STRING_ARRAY);
  }
  if (!node->get_parameter("dock_plugins", docks_plugins)) {
    RCLCPP_ERROR(node->get_logger(), "Charging dock plugins not given!");
    return false;
  }

  if (docks_plugins.size() < 1u) {
    RCLCPP_ERROR(node->get_logger(), "Charging dock plugins empty! Must provide 1.");
    return false;
  }

  for (size_t i = 0; i != docks_plugins.size(); i++) {
    try {
      std::string plugin_type = nav2_util::get_plugin_type_param(
        node, docks_plugins[i]);
      opennav_docking_core::ChargingDock::Ptr dock =
        dock_loader_.createUniqueInstance(plugin_type);
      RCLCPP_INFO(
        node->get_logger(), "Created charging dock plugin %s of type %s",
        docks_plugins[i].c_str(), plugin_type.c_str());
      dock->configure(node, docks_plugins[i], tf);
      dock_plugins_.insert({docks_plugins[i], dock});
    } catch (const std::exception & ex) {
      RCLCPP_FATAL(
        node->get_logger(), "Failed to create Charging Dock plugin. Exception: %s",
        ex.what());
      return false;
    }
  }

  return true;
}

bool DockDatabase::getDockInstances(const rclcpp_lifecycle::LifecycleNode::SharedPtr & node)
{
  using rclcpp::ParameterType::PARAMETER_STRING;
  using rclcpp::ParameterType::PARAMETER_STRING_ARRAY;

  // Attempt to obtain docks from separate file
  std::string dock_filepath;
  if (!node->has_parameter("dock_database")) {
    node->declare_parameter("dock_database", PARAMETER_STRING);
  }
  if (node->get_parameter("dock_database", dock_filepath)) {
    RCLCPP_INFO(
      node->get_logger(), "Loading dock from database file  %s.", dock_filepath.c_str());
    try {
      return utils::parseDockFile(dock_filepath, node, dock_instances_);
    } catch (YAML::ParserException & e) {
      RCLCPP_ERROR(
        node->get_logger(),
        "Dock database (%s) is malformed: %s.", dock_filepath.c_str(), e.what());
      return false;
    }
    return true;
  }

  // Attempt to obtain docks from parameter file
  std::vector<std::string> docks_param;
  if (!node->has_parameter("docks")) {
    node->declare_parameter("docks", PARAMETER_STRING_ARRAY);
  }
  if (node->get_parameter("docks", docks_param)) {
    RCLCPP_INFO(node->get_logger(), "Loading docks from parameter file.");
    return utils::parseDockParams(docks_param, node, dock_instances_);
  }

  RCLCPP_WARN(
    node->get_logger(),
    "Dock database filepath nor dock parameters set. "
    "Docking actions can only be executed specifying the dock pose via the action request. "
    "Or update the dock database via the reload_database service.");
  return true;
}

unsigned int DockDatabase::plugin_size() const
{
  return dock_plugins_.size();
}

unsigned int DockDatabase::instance_size() const
{
  return dock_instances_.size();
}

bool DockDatabase::updateDockPose(const std::string & dock_id, const geometry_msgs::msg::Pose & pose)
{
  std::lock_guard<std::recursive_mutex> lock(*mutex_);
  auto it = dock_instances_.find(dock_id);
  if (it != dock_instances_.end()) {
    RCLCPP_INFO(node_.lock()->get_logger(), "Updating dock pose for dock %s, from [%f, %f, %f] to [%f, %f, %f]",
      dock_id.c_str(), it->second.pose.position.x, it->second.pose.position.y,
      tf2::getYaw(it->second.pose.orientation), pose.position.x, pose.position.y, tf2::getYaw(pose.orientation));
    it->second.pose = pose;
    if (publish_debug_) {
      publishDocksDebug();
    }
    return true;
  }
  return false;
}

bool DockDatabase::registerDock(std::string & dock_id, const std::string & display_name)
{
  std::lock_guard<std::recursive_mutex> lock(*mutex_);
  geometry_msgs::msg::Pose pose;
  if (!getCurrentPositionDockPose(pose)) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Failed to get current position dock pose!");
    return false;
  }

  dock_id = generate_random_string();
  std::string tmp_display_name = display_name;
  if (tmp_display_name.empty()) {
    tmp_display_name = dock_id.size() > 6 ? dock_id.substr(dock_id.size() - 6) : dock_id;
  }

  Dock new_dock;
  new_dock.type = "simple_charging_dock";
  new_dock.frame = "map";
  new_dock.display_name = tmp_display_name;
  new_dock.pose = pose;
  new_dock.plugin = dock_plugins_["simple_charging_dock"];
  dock_instances_[dock_id] = new_dock;
  RCLCPP_INFO(node_.lock()->get_logger(), "Registered dock %s with pose [%f, %f, %f]",
    dock_id.c_str(), pose.position.x, pose.position.y, tf2::getYaw(pose.orientation));
  if (publish_debug_) {
    publishDocksDebug();
  }
  return true;
}

bool DockDatabase::isNearByRegistered(double distance_threshold)
{
  geometry_msgs::msg::Pose pose;
  if (!getCurrentPositionDockPose(pose)) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Failed to get current position dock pose!");
    return false;
  }

  for (const auto& pair : dock_instances_) {
    double distance = std::sqrt(std::pow(pair.second.pose.position.x - pose.position.x, 2) +
      std::pow(pair.second.pose.position.y - pose.position.y, 2));
    if (distance < distance_threshold) {
      RCLCPP_INFO(node_.lock()->get_logger(), "Registered dock %s within %f meters!", pair.first.c_str(), distance);
      return true;
    }
  }
  RCLCPP_INFO(node_.lock()->get_logger(), "No registered dock within %f meters!", distance_threshold);
  return false;
}

bool DockDatabase::isSameDock(const std::string & dock_id)
{
  auto it = dock_instances_.find(dock_id);
  if (it != dock_instances_.end()) {
    return isSameDock(it->second.pose);
  }
  return false;
}

bool DockDatabase::isSameDock(const geometry_msgs::msg::Pose & pose)
{
  geometry_msgs::msg::Pose current_pose;
  if (!getCurrentPositionDockPose(current_pose)) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Failed to get current position dock pose!");
    return false;
  }
  double distance = std::sqrt(std::pow(current_pose.position.x - pose.position.x, 2) +
    std::pow(current_pose.position.y - pose.position.y, 2));
  return distance < 0.1;
}

bool DockDatabase::getCurrentPositionDockPose(geometry_msgs::msg::Pose & pose)
{
  // get the robot pose in the frame
  geometry_msgs::msg::PoseStamped robot_pose;
  robot_pose.header.frame_id = "base_link";
  robot_pose.header.stamp = rclcpp::Time(0);
  try {
    tf2_buffer_->transform(robot_pose, robot_pose, "map");
  } catch (const tf2::TransformException & e) {
    RCLCPP_ERROR(node_.lock()->get_logger(), "Transform error: %s", e.what());
    return false;
  }
  double yaw = tf2::getYaw(robot_pose.pose.orientation);
  double total_offset = robot_docking_half_length_ + dock_offset_;
  if (dock_backwards_) {
    pose.position.x = robot_pose.pose.position.x - total_offset * std::cos(yaw);
    pose.position.y = robot_pose.pose.position.y - total_offset * std::sin(yaw);
    pose.orientation = nav2_util::geometry_utils::orientationAroundZAxis(yaw + M_PI);
  } else {
    pose.position.x = robot_pose.pose.position.x + total_offset * std::cos(yaw);
    pose.position.y = robot_pose.pose.position.y + total_offset * std::sin(yaw);
    pose.orientation = nav2_util::geometry_utils::orientationAroundZAxis(yaw);
  }
  return true;
}

void DockDatabase::publishDocksDebug()
{
  auto node = node_.lock();
  if (!node || !dock_debug_pub_) return;
  geometry_msgs::msg::PoseArray arr;
  arr.header.stamp = node->now();
  arr.header.frame_id = "map";
  for (const auto& pair : dock_instances_) {
    arr.poses.push_back(pair.second.pose);
  }
  dock_debug_pub_->publish(arr);
}

}  // namespace opennav_docking
