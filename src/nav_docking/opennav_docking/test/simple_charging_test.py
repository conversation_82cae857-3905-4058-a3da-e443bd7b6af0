#! /usr/bin/env python3
# Copyright 2024 Open Navigation LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from enum import Enum
import time
from math import sin, cos
from geometry_msgs.msg import Quaternion

from action_msgs.msg import GoalStatus
from geometry_msgs.msg import PoseStamped
from lifecycle_msgs.srv import GetState
from opennav_docking_msgs.action import DockRobot, UndockRobot
from nav2_msgs.action import NavigateToPose
import rclpy
from rclpy.action import ActionClient
from rclpy.duration import Duration
from rclpy.node import Node
import sys


class TaskResult(Enum):
    UNKNOWN = 0
    SUCCEEDED = 1
    CANCELED = 2
    FAILED = 3


class DockingTester(Node):

    def __init__(self):
        super().__init__(node_name='docking_tester')
        self.goal_handle = None
        self.result_future = None
        self.status = None
        self.feedback = None

        self.docking_client = ActionClient(self, DockRobot,
                                            'dock_robot')
        self.undocking_client = ActionClient(self, UndockRobot,
                                            'undock_robot')
        # Add navigation client
        self.navigation_client = ActionClient(self, NavigateToPose,
                                            'navigate_to_pose')

    def destroy_node(self):
        self.docking_client.destroy()
        self.undocking_client.destroy()
        self.navigation_client.destroy()
        super().destroy_node()

    def dockRobot(self, dock_pose, dock_type = ""):
        """Send a `DockRobot` action request."""
        print("Waiting for 'DockRobot' action server")
        while not self.docking_client.wait_for_server(timeout_sec=1.0):
            print('"DockRobot" action server not available, waiting...')

        goal_msg = DockRobot.Goal()
        goal_msg.use_dock_id = False
        # goal_msg.dock_id = dock_id  # if wanting to use ID instead
        goal_msg.dock_pose = dock_pose
        goal_msg.dock_type = dock_type
        # goal_msg.navigate_to_staging_pose = True  # if want to navigate before staging

        print('Docking at pose: ' + str(dock_pose) + '...')
        send_goal_future = self.docking_client.send_goal_async(goal_msg,
                                                                self._feedbackCallback)
        rclpy.spin_until_future_complete(self, send_goal_future)
        self.goal_handle = send_goal_future.result()

        if not self.goal_handle.accepted:
            print('Docking request was rejected!')
            return False

        self.result_future = self.goal_handle.get_result_async()
        return True

    def undockRobot(self, dock_type):
        """Send a `UndockRobot` action request."""
        print("Waiting for 'UndockRobot' action server")
        while not self.undocking_client.wait_for_server(timeout_sec=1.0):
            print('"UndockRobot" action server not available, waiting...')

        goal_msg = UndockRobot.Goal()
        goal_msg.dock_type = dock_type

        print('Undocking from dock of type: ' + str(dock_type) + '...')
        send_goal_future = self.undocking_client.send_goal_async(goal_msg,
                                                                 self._feedbackCallback)
        rclpy.spin_until_future_complete(self, send_goal_future)
        self.goal_handle = send_goal_future.result()

        if not self.goal_handle.accepted:
            print('Undocking request was rejected!')
            return False

        self.result_future = self.goal_handle.get_result_async()
        return True

    def navigateToPose(self, target_pose):
        """Send a `NavigateToPose` action request."""
        print("Waiting for 'NavigateToPose' action server")
        while not self.navigation_client.wait_for_server(timeout_sec=1.0):
            print('"NavigateToPose" action server not available, waiting...')

        goal_msg = NavigateToPose.Goal()
        goal_msg.pose = target_pose

        print('Navigating to pose: ' + str(target_pose) + '...')
        send_goal_future = self.navigation_client.send_goal_async(goal_msg,
                                                                 self._feedbackCallback)
        rclpy.spin_until_future_complete(self, send_goal_future)
        self.goal_handle = send_goal_future.result()

        if not self.goal_handle.accepted:
            print('Navigation request was rejected!')
            return False

        self.result_future = self.goal_handle.get_result_async()
        return True

    def isTaskComplete(self):
        """Check if the task request of any type is complete yet."""
        if not self.result_future:
            # task was cancelled or completed
            return True
        rclpy.spin_until_future_complete(self, self.result_future, timeout_sec=0.10)
        if self.result_future.result():
            self.status = self.result_future.result().status
            if self.status != GoalStatus.STATUS_SUCCEEDED:
                print(f'Task with failed with status code: {self.status}')
                return True
        else:
            # Timed out, still processing, not complete yet
            return False

        print('Task succeeded!')
        return True

    def _feedbackCallback(self, msg):
        self.feedback = msg.feedback
        return
    
    def cancelAction(self):
        if self.goal_handle:
            self.goal_handle.cancel_goal()

    def getFeedback(self):
        """Get the pending action feedback message."""
        return self.feedback

    def getResult(self):
        """Get the pending action result message."""
        if self.status == GoalStatus.STATUS_SUCCEEDED:
            return TaskResult.SUCCEEDED
        elif self.status == GoalStatus.STATUS_ABORTED:
            return TaskResult.FAILED
        elif self.status == GoalStatus.STATUS_CANCELED:
            return TaskResult.CANCELED
        else:
            return TaskResult.UNKNOWN

    def startup(self, node_name='docking_server'):
        # Waits for the node within the tester namespace to become active
        print(f'Waiting for {node_name} to become active..')
        node_service = f'{node_name}/get_state'
        state_client = self.create_client(GetState, node_service)
        while not state_client.wait_for_service(timeout_sec=1.0):
            print(f'{node_service} service not available, waiting...')

        req = GetState.Request()
        state = 'unknown'
        while state != 'active':
            print(f'Getting {node_name} state...')
            future = state_client.call_async(req)
            rclpy.spin_until_future_complete(self, future)
            if future.result() is not None:
                state = future.result().current_state.label
                print(f'Result of get_state: {state}')
            time.sleep(2)
        return

def quaternion_from_yaw(yaw):
    q = Quaternion()
    q.x = 0.0
    q.y = 0.0
    q.z = sin(yaw / 2.0)
    q.w = cos(yaw / 2.0)
    return q

def main():
    rclpy.init()

    tester = DockingTester()
    tester.startup()

    # 命令行参数解析
    if len(sys.argv) < 2:
        print("Usage: simple_charging_test.py [cycle|dock|undock]")
        return
    command = sys.argv[1]

    # 公共桩位和目标点
    dock_pose = PoseStamped()
    dock_pose.header.stamp = tester.get_clock().now().to_msg()
    dock_pose.header.frame_id = "map"
    dock_pose.pose.position.x = -9.3
    dock_pose.pose.position.y = -2.754
    dock_pose.pose.orientation = quaternion_from_yaw(3.14159)
    dock_type = "simple_charging_dock"

    target_pose = PoseStamped()
    target_pose.header.stamp = tester.get_clock().now().to_msg()
    target_pose.header.frame_id = "map"
    target_pose.pose.position.x = -7.3
    target_pose.pose.position.y = -2.754
    target_pose.pose.orientation = quaternion_from_yaw(3.14159)

    if command == "cycle":
        # 原有循环动作
        success_count = 0
        total_count = 0
        first_success_count = 0
        first_total_count = 0
        while True:
            total_count += 1
            first_total_count += 1
            tester.dockRobot(dock_pose, dock_type)
            i = 0
            docking_timeout = False
            while not tester.isTaskComplete():
                i += 1
                if i % 5 == 0:
                    print('Docking in progress...')
                if i // 5 > 5 and docking_timeout == False:
                    print('Docking timeout, considered as first attempt failure.')
                    docking_timeout = True
                time.sleep(1)
            if not docking_timeout:
                result = tester.getResult()
                if result == TaskResult.SUCCEEDED:
                    print('Docking succeeded!')
                    success_count += 1
                    first_success_count += 1
                elif result == TaskResult.CANCELED:
                    print('Docking canceled!')
                    continue
                elif result == TaskResult.FAILED:
                    print('Docking failed!')
                    continue
                else:
                    print('Docking has an invalid return status!')
                    break
            else:
                result = tester.getResult()
                if result == TaskResult.SUCCEEDED:
                    print('Docking succeeded after timeout!')
                    success_count += 1
                elif result == TaskResult.CANCELED:
                    print('Docking canceled after timeout!')
                    continue
                elif result == TaskResult.FAILED:
                    print('Docking failed after timeout!')
                    continue
                else:
                    print('Docking has an invalid return status after timeout!')
                    break
            percent = (success_count / total_count) * 100 if total_count > 0 else 0
            first_percent = (first_success_count / first_total_count) * 100 if first_total_count > 0 else 0
            print(f'Docking Success: {success_count}/{total_count} ({percent:.2f}%)')
            print(f'First Attempt Docking Success: {first_success_count}/{first_total_count} ({first_percent:.2f}%)')
            time.sleep(3)
            # Undock
            tester.undockRobot(dock_type)
            i = 0
            while not tester.isTaskComplete():
                i = i + 1
                if i % 5 == 0:
                    print('Undocking in progress...')
                time.sleep(1)
            result = tester.getResult()
            if result == TaskResult.SUCCEEDED:
                print('Undock succeeded!')
            elif result == TaskResult.CANCELED:
                print('Undock canceled!')
            elif result == TaskResult.FAILED:
                print('Undock failed!')
            else:
                print('Undock has an invalid return status!')
            print('Undock completed, navigating to target point...')
            tester.navigateToPose(target_pose)
            i = 0
            while not tester.isTaskComplete():
                i = i + 1
                if i % 5 == 0:
                    print('Navigation in progress...')
                time.sleep(1)
            result = tester.getResult()
            if result == TaskResult.SUCCEEDED:
                print('Navigation succeeded!')
            elif result == TaskResult.CANCELED:
                print('Navigation canceled!')
            elif result == TaskResult.FAILED:
                print('Navigation failed!')
            else:
                print('Navigation has an invalid return status!')
            print('Waiting 1 seconds before next docking cycle...')
            time.sleep(1)
    elif command == "dock":
        # 单独上桩
        tester.dockRobot(dock_pose, dock_type)
        i = 0
        while not tester.isTaskComplete():
            i += 1
            if i % 5 == 0:
                print('Docking in progress...')
            time.sleep(1)
        result = tester.getResult()
        if result == TaskResult.SUCCEEDED:
            print('Docking succeeded!')
        elif result == TaskResult.CANCELED:
            print('Docking canceled!')
        elif result == TaskResult.FAILED:
            print('Docking failed!')
        else:
            print('Docking has an invalid return status!')
    elif command == "undock":
        # 单独下桩
        tester.undockRobot(dock_type)
        i = 0
        while not tester.isTaskComplete():
            i += 1
            if i % 5 == 0:
                print('Undocking in progress...')
            time.sleep(1)
        result = tester.getResult()
        if result == TaskResult.SUCCEEDED:
            print('Undock succeeded!')
        elif result == TaskResult.CANCELED:
            print('Undock canceled!')
        elif result == TaskResult.FAILED:
            print('Undock failed!')
        else:
            print('Undock has an invalid return status!')
    else:
        print("Unknown command. Usage: simple_charging_test.py [cycle|dock|undock]")

if __name__ == '__main__':
    main()