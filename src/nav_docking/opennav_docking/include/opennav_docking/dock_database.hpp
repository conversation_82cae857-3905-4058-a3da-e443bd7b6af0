// Copyright (c) 2024 Open Navigation LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef OPENNAV_DOCKING__DOCK_DATABASE_HPP_
#define OPENNAV_DOCKING__DOCK_DATABASE_HPP_

#include <vector>
#include <memory>
#include <string>
#include <mutex>

#include "rclcpp/rclcpp.hpp"
#include "pluginlib/class_loader.hpp"
#include "pluginlib/class_list_macros.hpp"

#include "nav2_util/lifecycle_node.hpp"
#include "nav2_util/node_utils.hpp"
#include "nav2_util/simple_action_server.hpp"
#include "opennav_docking/utils.hpp"
#include "opennav_docking/types.hpp"
#include "opennav_docking_msgs/srv/reload_database.hpp"
#include "opennav_docking_msgs/srv/register_dock.hpp"
#include "opennav_docking_msgs/srv/get_all_docks.hpp"
#include "opennav_docking_msgs/srv/update_dock.hpp"
#include "opennav_docking_msgs/srv/delete_dock.hpp"
#include "opennav_docking_msgs/srv/set_docks.hpp"
#include "geometry_msgs/msg/pose_array.hpp"

namespace opennav_docking
{
/**
 * @class opennav_docking::DockDatabase
 * @brief An object to contain docks and docking plugins
 */
class DockDatabase
{
public:
  /**
   * @brief A constructor for opennav_docking::DockDatabase
   */
  explicit DockDatabase(std::shared_ptr<std::recursive_mutex> mutex = std::make_shared<std::recursive_mutex>());

  /**
   * @brief A setup function to populate database
   * @param parent Weakptr to the node to use to get interances and parameters
   * @param tf TF buffer
   * @return If successful
   */
  bool initialize(
    const rclcpp_lifecycle::LifecycleNode::WeakPtr & parent, std::shared_ptr<tf2_ros::Buffer> tf);

  /**
   * @brief A destructor for opennav_docking::DockDatabase
   */
  ~DockDatabase();

  /**
   * @brief An activation method
   */
  void activate();

  /**
   * @brief An deactivation method
   */
  void deactivate();

  /**
   * @brief Find a dock instance & plugin in the databases from ID
   * @param dock_id Id of dock to find
   * @return Dock pointer
   */
  Dock * findDock(const std::string & dock_id);

  /**
   * @brief Find a dock plugin to use for a given type
   * @param type Dock type to find plugin for
   * @return Dock plugin pointer
   */
  ChargingDock::Ptr findDockPlugin(const std::string & type);

  /**
   * @brief Get the number of docks in the database
   * @return unsigned int Number of dock instances in the database
   */
  unsigned int instance_size() const;

  /**
   * @brief Get the number of dock types in the database
   * @return unsigned int Number of dock types in the database
   */
  unsigned int plugin_size() const;

  /**
   * @brief Update the pose of a dock instance
   * @param dock_id Id of dock to update
   * @param pose New pose of dock
   * @return bool If successful
   */
  bool updateDockPose(const std::string & dock_id, const geometry_msgs::msg::Pose & pose);

  /**
   * @brief Register a dock instance
   * @param dock_id Id of dock to register
   * @param display_name Display name of dock
   * @return bool If successful
   */
  bool registerDock(std::string & dock_id, const std::string & display_name = "");

  /**
   * @brief Check if there is a registered dock within a certain distance
   * @param distance_threshold Distance threshold
   * @return bool If there is a registered dock within the distance threshold
   */
  bool isNearByRegistered(double distance_threshold);

  /**
   * @brief Check if the dock is the same as the current dock
   * @param dock_id Id of dock to check
   * @return bool If the dock is the same as the current dock
   */
  bool isSameDock(const std::string & dock_id);

  /**
   * @brief Check if the dock is the same as the current dock
   * @param pose Pose of dock to check
   * @return bool If the dock is the same as the current dock
   */
  bool isSameDock(const geometry_msgs::msg::Pose & pose);

protected:
  /**
   * @brief Populate database of dock type plugins
   * @param Node Node to get values from
   * @param tf TF buffer
   * @return bool If successful
   */
  bool getDockPlugins(
    const rclcpp_lifecycle::LifecycleNode::SharedPtr & node, std::shared_ptr<tf2_ros::Buffer> tf);

  /**
   * @brief Populate database of dock instances
   * @param Node Node to get values from
   */
  bool getDockInstances(const rclcpp_lifecycle::LifecycleNode::SharedPtr & node);

  /**
   * @brief Find a dock instance in the database from ID
   * @param dock_id Id of dock to find
   * @return Dock pointer
   */
  Dock * findDockInstance(const std::string & dock_id);

  /**
   * @brief Service request to reload database of docks
   * @param request Service request
   * @param response Service response
   */
  void reloadDbCb(
    const std::shared_ptr<opennav_docking_msgs::srv::ReloadDatabase::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::ReloadDatabase::Response> response);

  void registerDockCb(
    const std::shared_ptr<opennav_docking_msgs::srv::RegisterDock::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::RegisterDock::Response> response);

  void getAllDocksCb(
    const std::shared_ptr<opennav_docking_msgs::srv::GetAllDocks::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::GetAllDocks::Response> response);

  void updateDockCb(
    const std::shared_ptr<opennav_docking_msgs::srv::UpdateDock::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::UpdateDock::Response> response);

  void deleteDockCb(
    const std::shared_ptr<opennav_docking_msgs::srv::DeleteDock::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::DeleteDock::Response> response);

  void setDocksCb(
    const std::shared_ptr<opennav_docking_msgs::srv::SetDocks::Request> request,
    std::shared_ptr<opennav_docking_msgs::srv::SetDocks::Response> response);

  void publishDocksDebug();

  bool getCurrentPositionDockPose(geometry_msgs::msg::Pose & pose);

  rclcpp_lifecycle::LifecycleNode::WeakPtr node_;
  std::shared_ptr<tf2_ros::Buffer> tf2_buffer_;
  std::shared_ptr<std::recursive_mutex> mutex_;  // Don't reload database while actively docking
  DockPluginMap dock_plugins_;
  DockMap dock_instances_;
  pluginlib::ClassLoader<opennav_docking_core::ChargingDock> dock_loader_;
  rclcpp::Service<opennav_docking_msgs::srv::ReloadDatabase>::SharedPtr reload_db_service_;
  rclcpp::Service<opennav_docking_msgs::srv::RegisterDock>::SharedPtr register_dock_service_;
  rclcpp::Service<opennav_docking_msgs::srv::GetAllDocks>::SharedPtr get_all_docks_service_;
  rclcpp::Service<opennav_docking_msgs::srv::UpdateDock>::SharedPtr update_dock_service_;
  rclcpp::Service<opennav_docking_msgs::srv::DeleteDock>::SharedPtr delete_dock_service_;
  rclcpp::Service<opennav_docking_msgs::srv::SetDocks>::SharedPtr set_docks_service_;
  double robot_docking_half_length_;
  double dock_offset_;
  bool dock_backwards_;
  bool publish_debug_;
  rclcpp::Publisher<geometry_msgs::msg::PoseArray>::SharedPtr dock_debug_pub_;
};

}  // namespace opennav_docking

#endif  // OPENNAV_DOCKING__DOCK_DATABASE_HPP_
