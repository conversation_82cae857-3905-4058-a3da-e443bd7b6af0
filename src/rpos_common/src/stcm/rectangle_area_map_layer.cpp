
#include <stcm/rectangle_area_map_layer.h>
#include <core/metadata_internal.h>

namespace rpos { namespace stcm {

    const char* const RectangleAreaMapLayer::Type = "vnd.slamtec.map-layer/vnd.rectangle-area-map-v2+binary";
    const int RectangleAreaMapLayer::Version = 1;

    RectangleAreaMapLayer::RectangleAreaMapLayer()
    {
        setId("");
        metadata().set<int>(RPOS_COMPOSITEMAP_METADATA_KEY_VERSION, Version);
    }

    RectangleAreaMapLayer::~RectangleAreaMapLayer()
    {
        //
    }

    void RectangleAreaMapLayer::clear(void)
    {
        areas_.clear();
        this->MapLayer::clear();
    }

    std::string RectangleAreaMapLayer::getId() const
    { 
        return Id_;
    }

    void RectangleAreaMapLayer::setId(const std::string & id)
    {
        metadata().set(RPOS_COMPOSITEMAP_METADATA_KEY_ID, id); 
        Id_ = id;   
    }

    const std::vector<rpos::core::RectangleArea> & RectangleAreaMapLayer::areas() const
    {
        return areas_;
    }

    std::vector<rpos::core::RectangleArea> & RectangleAreaMapLayer::areas()
    {
        return areas_;
    }

}}
