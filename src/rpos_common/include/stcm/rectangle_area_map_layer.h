#pragma once

#include <core/pose.h>
#include <core/rectangle_area.h>
#include <core/metadata.h>
#include <stcm/map_layer.h>

namespace rpos { namespace stcm {

    class RectangleAreaMapLayer : public MapLayer
    {
    public:
        static const char* const Type;
        static const int Version;

        RectangleAreaMapLayer();
        virtual ~RectangleAreaMapLayer();

    public:
        virtual void clear(void);

        std::string getId() const;
        void setId(const std::string & id);
    public:
        const std::vector<rpos::core::RectangleArea> & areas() const;
        std::vector<rpos::core::RectangleArea> & areas();

    private:
        std::vector<rpos::core::RectangleArea> areas_;
        std::string Id_;
    };

}}
